import styled from 'styled-components';
import { IAccordionContent } from '../../Models';

/**
 * Styled component that serves as the container for accordion content.
 * Uses CSS Grid to layout its children with consistent spacing and
 * supports customizable background colors.
 *
 * @param {Object} props - The component props
 * @param {string} [props.background] - Optional background color for the content area
 * @param {ReactNode} props.children - The content to be displayed
 */
const Comp = styled(({ background, children, ...rest }) => (
  <div {...rest}>{children}</div>
))`
  && {
    display: grid;
    grid-template-rows: repeat(2, auto);
    /* gap: 0.75rem; */
    background-color: ${(props) => props.background || 'transparent'};
  }
`;

/**
 * A component that renders the expandable content section of an accordion item.
 * Provides a consistent layout and styling for content that is shown/hidden
 * when the accordion item is expanded/collapsed.
 *
 * @component
 * @param {Object} props - The component props
 * @param {ReactNode} props.children - The content to be displayed when the accordion item is expanded
 * @param {string} [props.background] - Optional background color for the content area
 *
 * @example
 * ```tsx
 * <AccordionContent background="#f5f5f5">
 *   <p>This is the expandable content of the accordion item.</p>
 *   <div>Additional content can be added here.</div>
 * </AccordionContent>
 * ```
 */
export const AccordionContent = ({ children, ...rest }: IAccordionContent) => (
  <Comp {...rest}>{children}</Comp>
);
