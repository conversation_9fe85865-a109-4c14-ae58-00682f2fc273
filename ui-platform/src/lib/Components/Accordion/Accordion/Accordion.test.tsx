import { fireEvent, render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider } from 'styled-components';
import { Accordion } from './Accordion';
import { AccordionItem } from '../AccordionItem/AccordionItem';
import { AccordionHeading } from '../AccordionHeading/AccordionHeading';
import { AccordionContent } from '../AccordionContent/AccordionContent';

// Mock theme for testing
const mockTheme = {
  SpacingXl: '2rem',
  SpacingMd: '1rem',
  SpacingSm: '0.5rem',
  SpacingXs: '0.25rem',
  RadiusXs: '4px',
  FontSize2: 14,
  FontSize3: 16,
  FontWeightsInter1: 400,
  FontWeightsInter2: 500,
  FontWeightsInter3: 600,
  FontFamiliesInter: 'Inter, sans-serif',
  ColorsInputsInverse: '#e5e5e5',
  ColorsButtonColorToolbarButtonsActivated: '#007bff',
  ColorsUtilityColorFocus: '#6c757d',
  ColorsButtonColorModuleActionsPrimary: '#f8f9fa',
  ColorsTypographyPrimary: '#212529',
  GapXs: '0.25rem',
};

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider theme={mockTheme}>{children}</ThemeProvider>
);

describe('Enhanced Accordion Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const createAccordionItems = () => [
    <AccordionItem key="1">
      <AccordionHeading>Section 1</AccordionHeading>
      <AccordionContent>Content 1</AccordionContent>
    </AccordionItem>,
    <AccordionItem key="2">
      <AccordionHeading>Section 2</AccordionHeading>
      <AccordionContent>Content 2</AccordionContent>
    </AccordionItem>,
    <AccordionItem key="3">
      <AccordionHeading>Section 3</AccordionHeading>
      <AccordionContent>Content 3</AccordionContent>
    </AccordionItem>,
  ];

  describe('Basic Functionality', () => {
    it('renders without crashing', () => {
      render(
        <TestWrapper>
          <Accordion>{createAccordionItems()}</Accordion>
        </TestWrapper>
      );
      
      expect(screen.getByTestId('accordion-list')).toBeInTheDocument();
    });

    it('renders all accordion items', () => {
      render(
        <TestWrapper>
          <Accordion>{createAccordionItems()}</Accordion>
        </TestWrapper>
      );
      
      expect(screen.getByText('Section 1')).toBeInTheDocument();
      expect(screen.getByText('Section 2')).toBeInTheDocument();
      expect(screen.getByText('Section 3')).toBeInTheDocument();
    });
  });

  describe('allowMultipleExpanded Functionality', () => {
    it('allows multiple items to be expanded when allowMultipleExpanded is true', () => {
      render(
        <TestWrapper>
          <Accordion allowMultipleExpanded={true}>
            {createAccordionItems()}
          </Accordion>
        </TestWrapper>
      );
      
      const section1 = screen.getByText('Section 1').closest('details');
      const section2 = screen.getByText('Section 2').closest('details');
      
      // Click both sections
      fireEvent.click(screen.getByText('Section 1'));
      fireEvent.click(screen.getByText('Section 2'));
      
      // Both should be open
      expect(section1).toHaveAttribute('open');
      expect(section2).toHaveAttribute('open');
    });

    it('allows only one item to be expanded when allowMultipleExpanded is false', () => {
      render(
        <TestWrapper>
          <Accordion allowMultipleExpanded={false}>
            {createAccordionItems()}
          </Accordion>
        </TestWrapper>
      );
      
      const section1 = screen.getByText('Section 1').closest('details');
      const section2 = screen.getByText('Section 2').closest('details');
      
      // Click first section
      fireEvent.click(screen.getByText('Section 1'));
      expect(section1).toHaveAttribute('open');
      
      // Click second section
      fireEvent.click(screen.getByText('Section 2'));
      expect(section1).not.toHaveAttribute('open');
      expect(section2).toHaveAttribute('open');
    });
  });

  describe('defaultExpandedIndexes Functionality', () => {
    it('expands items specified in defaultExpandedIndexes', () => {
      render(
        <TestWrapper>
          <Accordion defaultExpandedIndexes={[0, 2]}>
            {createAccordionItems()}
          </Accordion>
        </TestWrapper>
      );
      
      const section1 = screen.getByText('Section 1').closest('details');
      const section2 = screen.getByText('Section 2').closest('details');
      const section3 = screen.getByText('Section 3').closest('details');
      
      expect(section1).toHaveAttribute('open');
      expect(section2).not.toHaveAttribute('open');
      expect(section3).toHaveAttribute('open');
    });
  });

  describe('onToggle Callback', () => {
    it('calls onToggle callback when items are toggled', () => {
      const onToggle = vi.fn();
      
      render(
        <TestWrapper>
          <Accordion onToggle={onToggle}>
            {createAccordionItems()}
          </Accordion>
        </TestWrapper>
      );
      
      // Click first section
      fireEvent.click(screen.getByText('Section 1'));
      expect(onToggle).toHaveBeenCalledWith(0, true);
      
      // Click first section again to close
      fireEvent.click(screen.getByText('Section 1'));
      expect(onToggle).toHaveBeenCalledWith(0, false);
    });
  });

  describe('collapsible Functionality', () => {
    it('prevents closing the last open item when collapsible is false', () => {
      render(
        <TestWrapper>
          <Accordion 
            collapsible={false} 
            allowMultipleExpanded={false}
            defaultExpandedIndexes={[0]}
          >
            {createAccordionItems()}
          </Accordion>
        </TestWrapper>
      );
      
      const section1 = screen.getByText('Section 1').closest('details');
      
      // Section 1 should be open initially
      expect(section1).toHaveAttribute('open');
      
      // Try to close it - should remain open
      fireEvent.click(screen.getByText('Section 1'));
      expect(section1).toHaveAttribute('open');
    });

    it('allows closing items when collapsible is true', () => {
      render(
        <TestWrapper>
          <Accordion 
            collapsible={true}
            defaultExpandedIndexes={[0]}
          >
            {createAccordionItems()}
          </Accordion>
        </TestWrapper>
      );
      
      const section1 = screen.getByText('Section 1').closest('details');
      
      // Section 1 should be open initially
      expect(section1).toHaveAttribute('open');
      
      // Close it - should close
      fireEvent.click(screen.getByText('Section 1'));
      expect(section1).not.toHaveAttribute('open');
    });
  });

  describe('Null Children Handling', () => {
    it('handles null children gracefully', () => {
      const itemsWithNull = [
        createAccordionItems()[0],
        null,
        createAccordionItems()[1],
        undefined,
        createAccordionItems()[2],
      ];
      
      render(
        <TestWrapper>
          <Accordion>{itemsWithNull}</Accordion>
        </TestWrapper>
      );
      
      // Should render only the valid items
      expect(screen.getByText('Section 1')).toBeInTheDocument();
      expect(screen.getByText('Section 2')).toBeInTheDocument();
      expect(screen.getByText('Section 3')).toBeInTheDocument();
    });
  });

  describe('Integration with Original onClick', () => {
    it('calls both original onClick and internal toggle handler', () => {
      const originalOnClick = vi.fn();
      
      const itemsWithOnClick = [
        <AccordionItem key="1" onClick={originalOnClick}>
          <AccordionHeading>Section 1</AccordionHeading>
          <AccordionContent>Content 1</AccordionContent>
        </AccordionItem>,
      ];
      
      render(
        <TestWrapper>
          <Accordion>{itemsWithOnClick}</Accordion>
        </TestWrapper>
      );
      
      const section1 = screen.getByText('Section 1').closest('details');
      
      // Click the section
      fireEvent.click(screen.getByText('Section 1'));
      
      // Both handlers should be called
      expect(originalOnClick).toHaveBeenCalled();
      expect(section1).toHaveAttribute('open');
    });
  });
});
