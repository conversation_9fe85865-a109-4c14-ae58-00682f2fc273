import {
  Children,
  ReactElement,
  cloneElement,
  isValidElement,
  useMemo,
  useState,
} from 'react';
import styled from 'styled-components';
import { AccordionItemProps, AccordionListProps } from '../../Models';

/**
 * StyledDiv component that serves as the container for the Accordion.
 * Implemented as a styled div that passes through all props except children.
 */
const StyledDiv = styled(({ children, ...rest }) => (
  <div {...rest}>{children}</div>
))``;

/**
 * A controlled accordion component that manages the expansion state of its child items.
 * This component implements the accordion pattern where items can be expanded/collapsed,
 * with optional support for multiple items being expanded simultaneously.
 *
 * @component
 * @param {Object} props - The component props
 * @param {boolean} [props.allowMultipleExpanded=true] - If true, multiple accordion items can be expanded simultaneously
 * @param {ReactElement<AccordionItemProps>[]} props.children - The accordion items to be rendered
 * @param {Object} props.rest - Additional props to be spread to the root element
 *
 * @example
 * ```tsx
 * <Accordion allowMultipleExpanded={false}>
 *   <AccordionItem>
 *     <AccordionHeading>Section 1</AccordionHeading>
 *     <AccordionContent>Content 1</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 * ```
 */
export const Accordion = (props: AccordionListProps) => {
  const { allowMultipleExpanded = true, children, ...rest } = props;

  // State variable to track the expansion state of each accordion item.
  const [isAccOpen, setIsAccOpen] = useState<boolean[]>(
    children.map(
      (child: ReactElement<AccordionItemProps>) => !!child.props.isOpen
    )
  );

  // Memoized function to render the accordion items with their current expansion state.
  const accordions = useMemo(() => {
    // Handler to toggle accordion item open/close
    const handleToggle = (index: number) => {
      setIsAccOpen((prev) => {
        if (allowMultipleExpanded) {
          // Toggle only the clicked item
          return prev.map((open, i) => (i === index ? !open : open));
        } else {
          // Only one open at a time: open clicked, close others
          return prev.map((_, i) => (i === index ? !prev[i] : false));
        }
      });
    };
    return Children.map(
      children,
      (child: ReactElement<AccordionItemProps>, index) => {
        if (isValidElement(child)) {
          // Inject isOpen and onClick to control expansion
          return cloneElement(child, {
            ...child.props,
            isOpen: isAccOpen[index],
            onClick: () => handleToggle(index),
          });
        }
        return child;
      }
    );
  }, [isAccOpen, children, allowMultipleExpanded]);

  return (
    <StyledDiv data-testid="accordion-list" {...rest}>
      {accordions}
    </StyledDiv>
  );
};
