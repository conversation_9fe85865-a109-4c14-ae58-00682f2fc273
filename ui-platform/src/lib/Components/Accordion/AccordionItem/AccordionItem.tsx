import { MouseEvent } from 'react';
import styled from 'styled-components';
import { AccordionItemProps } from '../../Models';

/**
 * Styled details component that serves as the container for an accordion item.
 * Implements custom styling for the accordion item's expanded and collapsed states,
 * including cursor styling and chevron rotation animation.
 */
const Wrapper = styled(({ children, key, ...rest }) => (
  <details {...rest}>{children}</details>
))`
  && {
    cursor: pointer;
    border-radius: ${(props) => props.theme.RadiusXs};
    margin-bottom: ${(props) => props.theme.SpacingSm};

    &[open] {
      summary {
        border-color: ${(props) =>
          props.theme.ColorsButtonColorToolbarButtonsActivated};
        svg {
          transform: rotate(180deg) translateY(50%);
        }
      }
    }
  }
`;

/**
 * A component representing an individual item within an Accordion.
 * This component uses the HTML5 details/summary elements for native accessibility support
 * and implements custom styling and behavior for the accordion pattern.
 *
 * @component
 * @param {Object} props - The component props
 * @param {boolean} [props.isOpen=false] - Controls whether the accordion item is expanded
 * @param {string} [props.key] - Unique identifier for the accordion item
 * @param {(e: MouseEvent<HTMLElement>) => void} [props.onClick] - Callback function triggered when the item is clicked
 * @param {string} [props.layout] - Layout variant for the accordion item
 * @param {string} [props.className] - Additional CSS classes to apply
 * @param {ReactNode} props.children - The content of the accordion item (should include AccordionHeading and AccordionContent)
 *
 * @example
 * ```tsx
 * <AccordionItem isOpen={true} onClick={handleClick}>
 *   <AccordionHeading>Section Title</AccordionHeading>
 *   <AccordionContent>Section Content</AccordionContent>
 * </AccordionItem>
 * ```
 */
export const AccordionItem = ({
  isOpen = false,
  onClick,
  layout,
  className,
  children,
  ...rest
}: AccordionItemProps) => {
  /**
   * Handles click events on the accordion heading (summary element).
   * Only responds to clicks on the summary, allowing content interactions.
   *
   * @param {MouseEvent<HTMLElement>} e - The click event object
   */
  const onAccordionClick = (e: MouseEvent<HTMLElement>) => {
    // Only handle clicks on the summary element (AccordionHeading)
    // This allows interactive content in AccordionContent to work properly
    if (e.target instanceof HTMLElement) {
      const summary = e.currentTarget.querySelector('summary');
      const isClickOnSummary =
        summary?.contains(e.target) || e.target === summary;

      if (isClickOnSummary && onClick) {
        onClick(e);
      }
    }
  };

  return (
    <Wrapper open={isOpen} onClick={onAccordionClick} style={layout} {...rest}>
      {children[0]}
      {children[1]}
    </Wrapper>
  );
};
