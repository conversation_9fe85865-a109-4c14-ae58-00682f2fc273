import { MouseEvent, ReactElement } from 'react';
import { CSSProperties } from 'styled-components';

declare type WithChildren<T, C> = T & { children: C };

export interface IAccordionHeading {
  children: ReactElement | string;
}

export interface IAccordionContent {
  children: ReactElement | string;
}

export type AccordionItemProps = WithChildren<
  {
    key?: string;
    isOpen?: boolean;
    onClick?: (e: MouseEvent<HTMLElement>) => void;
    className?: string;
    layout?: CSSProperties;
  },
  [ReactElement<IAccordionHeading>, ReactElement<IAccordionContent>]
>;

export type AccordionListProps = WithChildren<
  {
    allowMultipleExpanded?: boolean;
    className?: string;
    defaultExpandedIndexes?: number[];
    onToggle?: (index: number, isOpen: boolean) => void;
    collapsible?: boolean;
  },
  React.ReactElement<AccordionItemProps>[]
>;
