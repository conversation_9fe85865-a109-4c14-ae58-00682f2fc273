import { ComponentPropsWithRef, ReactNode, useState } from 'react';
import styled, { useTheme } from 'styled-components';
import { Icon, IconTypes } from '../../Icons';
import { ImageWithLoader } from '../../Image';
import { ListItem, ListItemProps } from '../../ListItem/ListItem';
import { JobImageViewer } from '../JobImageViewer/JobImageViewer';

export interface JobImageItemProps {
  id: number;
  purpose: string;
  filename: string;
  job: number;
  claim: number;
  created: string;
  on_maven?: boolean;
  token: string;
  thumbnail: string;
  isBase64?: boolean;
  icon?: IconTypes;
}

const JobImageContainer = styled(({ children, ...rest }) => (
  <ListItem {...rest}>{children}</ListItem>
))<{ children: ReactNode }>`
  height: auto;
  border-radius: 4px;
  width: 100%;
  display: grid;
  grid-auto-flow: row;
  align-items: center;
  justify-content: center;
  padding: 4px;
  box-sizing: border-box;
  gap: 4px;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize2}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  cursor: ${(props) => (props.disabled ? 'default' : 'pointer')};
  transition: all 0.2s ease-in-out 0.1s;
`;

const ItemImage = styled.div`
  width: 130px;
  height: 96px;
  border-radius: 2px;
  padding: 4px;
  margin-bottom: 4px;
  display: grid;
  align-items: left;
  position: relative;
  justify-items: center;
  align-content: center;
`;

const ItemBody = styled(
  ({
    name,
    className,
    ...rest
  }: Partial<{ name: string; className: string }>) => (
    <div {...{ className }}>{name}</div>
  )
)<{ purpose: string } & ListItemProps>`
  position: relative;
  font-size: ${(props) => props.theme.FontSize2}px;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  text-align: left;
  display: grid;
  align-items: left;
  height: 30px;
  padding: 4px;
  text-align: center;
`;

/**
 * A single job image item with a name, date, time, team member name, and
 * an optional icon.
 *
 * @param {string} src - The source URL of the image to display.
 * @param {string} name - The name of the job image.
 * @param {string} date - The date associated with the image.
 * @param {string} time - The time associated with the image.
 * @param {string} TeamMemberName - The name of the team member associated with the image.
 * @param {'image' | 'icon'} mediaType - The type of media to display, either an image or an icon.
 * @param {IconTypes} [icon] - The icon type to display when mediaType is set to 'icon'.
 */
export const JobImageItem = ({
  purpose,
  created,
  thumbnail,
  filename,
  icon = 'file-07',
  ...rest
}: any) => {
  const iconStroke = useTheme().ColorsIconColorTertiary;

  // Check if thumbnail is a raw base64 string.
  const isThumbnailBase64 =
    thumbnail &&
    !thumbnail.startsWith('http') &&
    !thumbnail.startsWith('data:');
  const finalSrc = isThumbnailBase64
    ? `data:image/png;base64,${thumbnail}`
    : thumbnail;

  const [viewerOpen, setViewerOpen] = useState(false);

  /**
   * Handles click event on the job image item. This will navigate to
   * the enlarged job image/document.
   */
  const handleClick = () => {
    setViewerOpen(true);
  };

  const handleCloseViewer = () => {
    setViewerOpen(false);
  };

  return (
    <>
      <JobImageContainer onClick={handleClick} {...rest}>
        <ItemImage>
          {filename !== '' && thumbnail !== null ? (
            <ImageWithLoader
              src={finalSrc}
              alt={purpose}
              width={130}
              height={96}
              style={{ borderRadius: '.5rem' }}
            />
          ) : (
            <Icon
              type={icon}
              size={96}
              width="100%"
              height={96}
              color={iconStroke}
              strokeWidth="1px"
            />
          )}
        </ItemImage>
        {purpose && <ItemBody name={purpose} {...rest} />}
      </JobImageContainer>

      {viewerOpen && (
        <JobImageViewer src={finalSrc} onClose={handleCloseViewer} />
      )}
    </>
  );
};
