// external module imports
import { Box, Typography } from '@mui/material';
import { useCallback, useMemo, useRef, useState } from 'react';
import { DropzoneProps, useDropzone } from 'react-dropzone';
import { Control, Controller, FieldError, set } from 'react-hook-form';
import styled, { useTheme } from 'styled-components';
// internal module imports
import { ActionConfig } from '../../Engine';
import { Text } from '../../Fragments/Text/Text';
import { Files, setFileTypes } from '../../Utilities/fileUploadMimeTypes';
import { TextButton } from '../Buttons/TextButton/TextButton';
import { IconTypes } from '../Icons';
import { Icon } from '../Icons/Icon';

export interface DragAndDropProps {
  fileData?: any;
  filename?: string;
  onDrop: DropzoneProps['onDrop'];
  name: string;
  toggleButton?: boolean;
  iconLeft?: IconTypes;
  iconRight?: IconTypes;
  error?: FieldError | undefined | null;
  label?: string;
  control?: Control;
  instructions?: string;
  className?: string;
  uploadState?: 'activeFile' | 'error' | 'default' | 'focus';
  fileSizeLimit?: number;
  fileTypesAllowed?: Files[];
  disabled?: boolean;
  _onButtonClick?: (c?: any) => void;
  _blockUpload?: boolean;
  // New props for base64 upload support
  uploadMode?: 'binary' | 'base64';
  jobId?: string | number;
  purpose?: string;
  onBase64Upload?:
    | ((data: {
        data: string;
        filename: string;
        job_id: string | number;
        purpose: string;
      }) => void)
    | ActionConfig[];
  _callClientAction?: (
    config: ActionConfig | ActionConfig[],
    async?: boolean,
    concurrencyLimit?: number,
    debug?: boolean
  ) => void;
}

const Wrapper = styled.div`
  width: 100%;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto 1fr;
`;

const Container = styled.div`
  width: 100%;
  height: 40px;
  grid-column: 1;
  grid-row: 2;
  border-radius: ${(props) => props.theme.GapXs};

  display: grid;
  grid-template-columns: auto 1fr auto auto;
  grid-template-rows: 1fr;
  justify-items: start;
  align-items: center;
  box-sizing: border-box;
`;

const Label = styled.div`
  grid-column: 1;
  grid-row: 1;
  padding: 1px;
`;

const DocumentIcon = styled(Icon)`
  padding-left: 10px;
  grid-column: 1;
  grid-row: 1;
`;

const FileName = styled.div`
  grid-column: 2;
  grid-row: 1;
  padding: 1px;
`;

const RightIcon = styled(Icon)`
  grid-column: 3;
  grid-row: 1;
  padding-right: 6px;
`;

const Button = styled.div`
  grid-column: 4;
  grid-row: 1;
  padding-right: 10px;
`;

/**
 * @function DragAndDrop
 * @description A component that allows users to drag and drop a file or click to upload a file.
 * @prop {string} label - The label for the drag and drop component.
 * @prop {IconTypes} iconLeft - The icon to display on the left side of the component.
 * @prop {IconTypes} iconRight - The icon to display on the right side of the component.
 * @prop {boolean} toggleButton - Whether to show a button to toggle the drop zone.
 * @prop {any} fileData - The data to display.
 * @prop {string} filename - The filename to display.
 * @prop {Function} onDrop - The function to call when a file is dropped.
 * @prop {Error} error - The error to display if there is one.
 * @prop {string} instructions - The instructions to display below the component.
 * @prop {string} className - The class name to apply to the wrapper element.
 * @prop {Control} control - The control to use for the component.
 * @prop {string} name - The name to use for the component.
 * @prop {string} uploadState - The state of the upload. Can be 'default', 'activeFile', 'focus', or 'error'.
 * @prop {number} fileSizeLimit - The maximum file size to allow.
 * @prop {Files[]} fileTypesAllowed - The file types to allow.
 * @prop {string} uploadMode - The upload mode: 'binary' or 'base64'.
 * @prop {string|number} jobId - The job ID for base64 uploads.
 * @prop {string} purpose - The purpose for base64 uploads.
 * @prop {Function} onBase64Upload - Callback for base64 uploads.
 * @returns {JSX.Element} The drag and drop component.
 */
export function DragAndDrop({
  label,
  iconLeft,
  iconRight,
  toggleButton = true,
  fileData,
  filename,
  onDrop,
  error,
  instructions,
  className,
  control,
  name,
  uploadState = 'default',
  fileSizeLimit = 5242880,
  fileTypesAllowed = ['pdf'],
  disabled = false,
  _onButtonClick,
  _blockUpload = false,
  uploadMode = 'binary',
  jobId,
  purpose,
  onBase64Upload,
  _callClientAction,
}: DragAndDropProps) {
  const tokensTheme = useTheme();
  const [fName, setFName] = useState(filename || '');
  const onChangeRef = useRef<(val: any) => void>();

  function disabledFn() {
    return;
  }

  // Helper function to convert file to base64
  const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix (e.g., "data:application/pdf;base64,")
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = (error) => reject(error);
    });
  };

  // Enhanced onDrop handler that supports both modes
  const handleDrop = useCallback(
    async (acceptedFiles: File[], fileRejections: any[], evt: any) => {
      if (
        uploadMode === 'base64' &&
        onBase64Upload &&
        acceptedFiles.length > 0
      ) {
        try {
          const file = acceptedFiles[0];
          const base64Data = await convertFileToBase64(file);
          setFName(file.name);

          if (typeof onBase64Upload === 'function') {
            onBase64Upload({
              data: base64Data,
              filename: file.name,
              job_id: jobId || '',
              purpose: purpose || '',
            });
          } else if (Array.isArray(onBase64Upload) && _callClientAction) {
            const b64FileData = {
              data: base64Data,
              filename: file.name,
              job_id: jobId || '',
              purpose: purpose || '',
            };
            _callClientAction(
              onBase64Upload.map((o) => ({
                ...o,
                param: b64FileData,
              }))
            );
          } else {
            console.error('onBase64Upload is not a function or ActionConfig[]');
          }
          
          if (onChangeRef.current) {
            onChangeRef.current({
              data: base64Data,
              filename: file.name,
              job_id: jobId || '',
              purpose: purpose || '',
            });
          }
        } catch (error) {
          console.error('Error converting file to base64:', error);
        }
      } else {
        // Fall back to original onDrop behavior for binary mode
        if (onDrop) {
          onDrop(acceptedFiles, fileRejections, evt);
        }
      }
    },
    [uploadMode, onBase64Upload, jobId, purpose, onDrop]
  );

  const { getRootProps, getInputProps, open, acceptedFiles, fileRejections } =
    useDropzone({
      onDrop: disabled ? disabledFn : handleDrop,
      maxFiles: 1,
      maxSize: fileSizeLimit,
      accept: setFileTypes(fileTypesAllowed),
      noClick: true,
      noKeyboard: true,
    });

  const dynamicStyle = useMemo(() => {
    const borderColors = {
      error: `${tokensTheme.ColorsUtilityColorError}`,
      default: `${tokensTheme.ColorsStrokesGrey}`,
      activeFile: `${tokensTheme.ColorsUtilityColorSuccess}`,
      focus: `${tokensTheme.ColorsUtilityColorFocus}`,
    };
    return {
      borderColor: fileRejections[0]
        ? borderColors.error
        : borderColors[uploadState] ?? borderColors.default,
      borderWidth: '2px',
      borderStyle: 'dotted',
    };
  }, [fileRejections, uploadState, tokensTheme]);

  const hoverStyle = {
    borderColor: `${tokensTheme.ColorsStrokesDefault}`,
  };

  const [isHovered, setIsHovered] = useState(false);
  // const [isFile, setIsFile] = useState(false);
  // const [error, setError] = useState<FieldError | undefined | null>(null);
  const execButtonClicked = useCallback(
    () => _onButtonClick && _onButtonClick(),
    [_onButtonClick]
  );
  const blockUpload = useMemo(() => _blockUpload, [_blockUpload]);

  return (
    <Controller
      name={name}
      control={control}
      render={({
        field: { name: nm, value, onChange },
        fieldState: { error },
      }) => {
        onChangeRef.current = onChange;
        return (
          <Wrapper {...getRootProps({ className: `dropzone ${className}` })}>
            <input {...getInputProps()} name={nm} />
            {label && (
              <Label>
                <Text
                  textItems={[
                    {
                      text: label, //this style needs to be dekstop/form/heading
                      options: {
                        format: 'form',
                        type: 'heading',
                      },
                    },
                  ]}
                />
              </Label>
            )}
            <Container
              style={
                isHovered
                  ? disabled
                    ? {
                        borderColor: `${tokensTheme.ColorsStrokesGrey}`,
                        borderWidth: '2px',
                        borderStyle: 'dotted',
                      }
                    : { ...dynamicStyle, ...hoverStyle }
                  : dynamicStyle
              }
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              {(!!iconLeft || !!acceptedFiles) && (
                <DocumentIcon
                  type={acceptedFiles ? 'file-07' : iconLeft}
                  color="white"
                />
              )}
              {fileRejections[0] ? (
                <Box>
                  <FileName>
                    <Text
                      textItems={[
                        {
                          text: fileRejections[0].errors[0].message.replace(
                            '5242880 bytes',
                            '5mb'
                          ),
                          options: {
                            format: 'form',
                            type: 'input_text',
                          },
                        },
                      ]}
                    />
                  </FileName>
                </Box>
              ) : (
                <Box>
                  <Typography
                    sx={{
                      display: 'flex',
                      paddingLeft: '10px',
                      justifyContent: 'flex-start',
                      fontFamily: tokensTheme.DesktopFormsHeading.fontFamily,
                      fontSize: tokensTheme.DesktopFormsHeading.fontSize,
                      fontWeight: tokensTheme.DesktopFormsHeading.fontWeight,
                      color: 'white',
                    }}
                  >
                    {value?.file?.name ||
                      value?.filename ||
                      fileData?.filename ||
                      // fName ||
                      'No files uploaded'}
                  </Typography>
                </Box>
              )}
              {!!iconRight && <RightIcon type={iconRight} />}
              {toggleButton && (
                <Button>
                  <TextButton
                    disabled={disabled}
                    onClick={() => {
                      execButtonClicked();
                      if (blockUpload) return;
                      open();
                    }}
                    actiontype={
                      acceptedFiles.length === 1 || fileData?.filename || fName
                        ? 'default'
                        : 'attention'
                    }
                    btnValue={
                      acceptedFiles.length === 1 || fileData?.filename || fName
                        ? 'Replace'
                        : 'Browse'
                    }
                  />
                </Button>
              )}
            </Container>
            {error ? (
              <Box component="div">
                <Typography
                  sx={{
                    color: tokensTheme.ColorsUtilityColorError,
                    fontFamily: tokensTheme.DesktopFormsHeading.fontFamily,
                    fontSize: tokensTheme.DesktopFormsHeading.fontSize,
                    fontWeight: tokensTheme.DesktopFormsHeading.fontWeight,
                  }}
                >
                  {error ? error.message : ''}
                </Typography>
              </Box>
            ) : (
              instructions && (
                <Box component="div">
                  <Typography>{instructions}</Typography>
                </Box>
              )
            )}
          </Wrapper>
        );
      }}
    />
  );
}
