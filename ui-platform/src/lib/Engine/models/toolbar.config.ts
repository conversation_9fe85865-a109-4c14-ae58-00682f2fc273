import { IconTypes } from '../../Components';
import { ActionConfig } from './action.config';
import { MenuItemConfig } from './menu-list-item.config';

export interface ToolbarConfig {
  modulesList: {
    name: string;
    path: string;
    icon?: IconTypes;
    alert?: boolean;
    alertIcon?: { type: IconTypes; color?: 'string' };
    active?: boolean;
  }[];
  // toolbarTabsConfig: ToolbarTabsConfig;
  title?: string;
  email: string;
  username: string;
  menuItems: MenuItemConfig[];
  // optionsHandler?: (e?: MouseEvent<HTMLElement>) => void;
  // avatarHandler?: (e?: MouseEvent<HTMLElement>) => void;
  buttonText?: string;
  showNeedHelpButton?: boolean;
  displaySort?: boolean;
  buttonOnClick?: ActionConfig[];
  profileDisabled?: boolean;
  // pingHandler?: (e?: MouseEvent<HTMLElement>) => void;
  pingDisabled?: boolean;
  className?: string;
  image?: string;
  alertIndicator?: boolean;
  alertIndicatorIcon?: { type: IconTypes; color?: string };
}
