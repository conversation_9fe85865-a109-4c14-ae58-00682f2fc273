import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { v4 as uuidv4 } from 'uuid';
import { IRecentActivity, IRecentActivityStore } from '../models/IRecentActivity';
import { StorageKeys } from '../models/StorageKeys';

const MAX_ITEMS_PER_USER = 20;

const useRecentActivityStore = create<IRecentActivityStore>()(
  persist(
    (set, get) => {
      // Debug: Log what's in localStorage on store initialization
      console.log('RecentActivityStore: Initializing store');
      console.log('RecentActivityStore: Storage key:', StorageKeys.RECENT_ACTIVITY);
      console.log('RecentActivityStore: localStorage content:', localStorage.getItem(StorageKeys.RECENT_ACTIVITY));
      
      return {
        items: [],
        
        addItem: (item: Omit<IRecentActivity, 'id'>) => {
          const newItem: IRecentActivity = {
            ...item,
            id: uuidv4(),
          };
          
          console.log('RecentActivityStore: Adding item:', newItem);
          console.log('RecentActivityStore: Storage key:', StorageKeys.RECENT_ACTIVITY);
          console.log('RecentActivityStore: Current localStorage:', localStorage.getItem(StorageKeys.RECENT_ACTIVITY));
          
          set((state) => {
            // Get existing items for this user
            const userItems = state.items.filter(existing => existing.userID === item.userID);
            
            // Check if we need to remove the oldest item (like Angular does)
            let updatedItems = [...state.items];
            
            if (userItems.length >= MAX_ITEMS_PER_USER) {
              // Find the oldest item for this user and remove it
              const oldestItem = userItems.sort((a, b) => 
                new Date(a.access_date).getTime() - new Date(b.access_date).getTime()
              )[0];
              
              if (oldestItem) {
                updatedItems = updatedItems.filter(item => item.id !== oldestItem.id);
              }
            }
            
            // Add the new item (no duplicate checking like Angular)
            updatedItems.push(newItem);
            
            console.log('RecentActivityStore: Updated items:', updatedItems);
            
            return { items: updatedItems };
          });
        },
        
        removeItem: (id: string) => {
          set((state) => ({
            items: state.items.filter(item => item.id !== id)
          }));
        },
        
        clearAll: () => {
          set({ items: [] });
        },
        
        getItems: () => {
          return get().items;
        },
        
        getItemsByUser: (userID: number) => {
          const state = get();
          const userItems = state.items
            .filter(item => item.userID === userID)
            .sort((a, b) => new Date(b.access_date).getTime() - new Date(a.access_date).getTime());
          
          console.log('RecentActivityStore: getItemsByUser', {
            userID,
            allItems: state.items,
            userItems: userItems
          });
          
          return userItems;
        },
      };
    },
    {
      name: StorageKeys.RECENT_ACTIVITY,
      // Optional: Add version for future migrations
      version: 1,
    }
  )
);

export default useRecentActivityStore; 