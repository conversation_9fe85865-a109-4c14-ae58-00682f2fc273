import React from 'react';
import styled from 'styled-components';
import DocumentPreview, {
  DocumentPreviewProps,
} from '../../Components/Inputs/DocumentPreview/DocumentPreview';
import { Text } from '../../Fragments/Text/Text';

export type PDFPreviewProps = {
  documents: DocumentPreviewProps[];
  isBase64?: boolean;
  fallbackMessage?: string;
};

const Container = styled.div<{ gap?: string }>`
  display: grid;
  gap: ${(props) => props.gap || '16px'};
  overflow: hidden;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
`;

export const PDFPreview: React.FC<PDFPreviewProps> = ({
  documents,
  fallbackMessage = 'No documents to display',
}) => {
  if (!documents || documents.length === 0) {
    return (
      <Container style={{ justifyContent: 'center' }}>
        <div style={{ fontWeight: 'bold', fontSize: '1.2em' }}>{fallbackMessage}</div>
      </Container>
    );
  }

  return (
    <Container>
      {documents.map(({ documentUrl, isBase64 }, index) => (
        <DocumentPreview
          key={index}
          documentUrl={documentUrl}
          isBase64={isBase64}
          fallbackMessage={fallbackMessage}
        />
      ))}
    </Container>
  );
};
