import { Meta, StoryObj } from '@storybook/react';
import { PDFPreview as DocumentPreview } from './PDFviewer';

const meta: Meta<typeof DocumentPreview> = {
  title: 'Fragments/PDFviewer',
  component: DocumentPreview,
  argTypes: {
    documents: { control: { type: 'object' } },
  },
};
export default meta;

type Story = StoryObj<typeof DocumentPreview>;

export const Default: Story = {
  args: {
    fallbackMessage: 'No document available',
    documents: [
      {
        documentUrl:
          'https://s28.q4cdn.com/392171258/files/doc_downloads/test.pdf',
        isBase64: false,
      },
    ],
  },
};

export const WithoutFiles: Story = {
  args: {
    fallbackMessage: 'Oops, something went wrong',
    documents: [],
  },
};
