import styled, { CSSProperties } from 'styled-components';

import Keycloak from 'keycloak-js';
import {
  ComponentPropsWithoutRef,
  useCallback,
  useMemo,
  useState,
} from 'react';
import { Accordion } from '../../Components/Accordion/Accordion/Accordion';
import { AccordionContent } from '../../Components/Accordion/AccordionContent/AccordionContent';
import { AccordionHeading } from '../../Components/Accordion/AccordionHeading/AccordionHeading';
import { AccordionItem } from '../../Components/Accordion/AccordionItem/AccordionItem';
import { IconTypes } from '../../Components/Icons';
import {
  ItemClickApiConfig,
  JobImageListItem,
} from '../../Components/JobImage/JobImageListItem/JobImageListItem';

// Improved TypeScript interfaces
export interface DocumentFile {
  id: string | number;
  purpose?: string;
  filename?: string;
  created?: string;
  thumbnail?: string | null;
  mediaType?: string;
  job?: number;
  claim?: number;
  on_maven?: boolean;
  token?: string;
  isBase64?: boolean;
  [key: string]: any; // Allow additional properties for flexibility
}

export interface DocumentCategory {
  heading: string;
  match: (doc: DocumentFile) => boolean;
  icon?: IconTypes;
  defaultOpen?: boolean;
}

interface Category {
  heading: string;
  content: DocumentFile[];
  isOpen: boolean;
  onClick?: () => void;
  layout?: CSSProperties;
}

export interface DocumentsViewProps {
  content: DocumentFile[];
  categories?: DocumentCategory[];
  onItemClickConfig?: ItemClickApiConfig;
  fileDataKey?: string;
  _keycloak?: Keycloak;
  allowMultipleExpanded?: boolean;
  showLatestDocuments?: boolean;
  searchable?: boolean;
  onError?: (error: string) => void;
  className?: string;
}

// Default categories configuration
const defaultCategories: DocumentCategory[] = [
  {
    heading: 'Photos',
    match: (doc: DocumentFile) =>
      /image/i.test(doc.purpose || '') || /jpg/i.test(doc.filename || ''),
    icon: 'file-07',
    defaultOpen: false,
  },
  {
    heading: 'Quotations',
    match: (doc: DocumentFile) => /quotation/i.test(doc.purpose || ''),
    icon: 'file-07',
    defaultOpen: false,
  },
  {
    heading: 'Invoices',
    match: (doc: DocumentFile) => /invoice/i.test(doc.purpose || ''),
    icon: 'file-07',
    defaultOpen: false,
  },
  {
    heading: 'Reports',
    match: (doc: DocumentFile) => /report/i.test(doc.purpose || ''),
    icon: 'file-07',
    defaultOpen: false,
  },
];

const LatestDocumentsSubtitle = styled.div`
  font-size: 14px;
  font-weight: 400;
  line-height: 16.94px;
  color: #c4c4c4;
  margin: ${(props) => props.theme.SpacingXl};
  text-align: center;
`;

const Container = styled.div`
  display: grid;
  grid-template-rows: auto 1fr;
  width: 100%;
`;

const ErrorContainer = styled.div`
  padding: 2rem;
  text-align: center;
  color: #e74c3c;
  background-color: #fdf2f2;
  border-radius: 8px;
  border: 1px solid #fecaca;
`;

const RetryButton = styled.button`
  padding: 0.5rem 1rem;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1rem;

  &:hover {
    background-color: #c0392b;
  }
`;

const SearchContainer = styled.div`
  padding: ${(props) => props.theme.SpacingMd};
  margin-bottom: ${(props) => props.theme.SpacingMd};
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${(props) => props.theme.SpacingSm};
  border: 1px solid ${(props) => props.theme.ColorsInputsInverse};
  border-radius: ${(props) => props.theme.RadiusXs};
  font-size: ${(props) => props.theme.FontSize2}px;
  font-family: ${(props) => props.theme.FontFamiliesInter};

  &:focus {
    outline: none;
    border-color: ${(props) =>
      props.theme.ColorsButtonColorToolbarButtonsActivated};
  }
`;

const EmptyStateContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${(props) => `${props.theme.SpacingXl} ${props.theme.SpacingMd}`};
  text-align: center;
  color: ${(props) => props.theme.ColorsUtilityColorFocus};
  background-color: ${(props) =>
    props.theme.ColorsButtonColorModuleActionsPrimary};
  border-radius: ${(props) => props.theme.RadiusXs};
  margin: ${(props) => props.theme.SpacingMd} 0;
`;

const EmptyStateTitle = styled.h3`
  margin: ${(props) => props.theme.SpacingSm} 0;
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter2};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
`;

const EmptyStateMessage = styled.p`
  margin: 0;
  font-size: ${(props) => props.theme.FontSize2}px;
  color: ${(props) => props.theme.ColorsUtilityColorFocus};
`;

/**
 * Renders a view for displaying a list of documents in an accordion format.
 * @param props - The component props
 * @returns JSX element representing the documents view.
 */
export function DocumentsView({
  content,
  categories = defaultCategories,
  onItemClickConfig,
  fileDataKey = 'file',
  _keycloak,
  allowMultipleExpanded = false,
  showLatestDocuments = true,
  searchable = false,
  onError,
  className,
}: DocumentsViewProps) {
  // State management for accordion sections
  const [openSections, setOpenSections] = useState<Set<string>>(() => {
    const initialOpen = new Set<string>();
    categories.forEach((cat) => {
      if (cat.defaultOpen) {
        initialOpen.add(cat.heading);
      }
    });
    return initialOpen;
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Toggle section handler with proper state management
  const toggleSection = useCallback(
    (heading: string) => {
      setOpenSections((prev) => {
        const newSet = new Set(prev);

        if (allowMultipleExpanded) {
          // Special handling for Latest Documents - it should close all others when opened
          if (heading === 'Latest Documents') {
            if (newSet.has(heading)) {
              newSet.delete(heading);
            } else {
              newSet.clear();
              newSet.add(heading);
            }
          } else {
            // For other sections, close Latest Documents if it's open, then toggle normally
            if (newSet.has('Latest Documents')) {
              newSet.delete('Latest Documents');
            }
            if (newSet.has(heading)) {
              newSet.delete(heading);
            } else {
              newSet.add(heading);
            }
          }
        } else {
          // Only one section open at a time
          if (newSet.has(heading)) {
            newSet.clear();
          } else {
            newSet.clear();
            newSet.add(heading);
          }
        }
        return newSet;
      });
    },
    [allowMultipleExpanded]
  );

  // Keyboard navigation handler
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent, heading: string) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        toggleSection(heading);
      }
    },
    [toggleSection]
  );

  // Memoized item click configuration
  const itemClickConfig = useMemo(() => {
    console.log('propsData', { content, onItemClickConfig, fileDataKey });
    return {
      ...onItemClickConfig,
      token: onItemClickConfig?.token
        ? onItemClickConfig.token
        : _keycloak?.token,
    };
  }, [
    _keycloak,
    onItemClickConfig,
    content,
    fileDataKey,
  ]) as ItemClickApiConfig;

  // Filter documents based on search term
  const filteredContent = useMemo(() => {
    if (!searchable || !searchTerm.trim()) {
      return content;
    }

    const searchLower = searchTerm.toLowerCase();
    return content.filter(
      (doc) =>
        doc.purpose?.toLowerCase().includes(searchLower) ||
        doc.filename?.toLowerCase().includes(searchLower) ||
        doc.id?.toString().toLowerCase().includes(searchLower)
    );
  }, [content, searchTerm, searchable]);

  // Memoized document categorization with error handling
  const categorizedDocuments = useMemo(() => {
    try {
      setIsLoading(true);
      setError(null);

      const result = categories.map((cat) => ({
        heading: cat.heading,
        content: filteredContent.filter(cat.match),
        isOpen: openSections.has(cat.heading),
        onClick: () => toggleSection(cat.heading),
        layout: {},
      }));

      setIsLoading(false);
      return result;
    } catch (err) {
      const errorMessage = 'Failed to categorize documents';
      setError(errorMessage);
      setIsLoading(false);
      onError?.(errorMessage);
      return [];
    }
  }, [filteredContent, categories, openSections, toggleSection, onError]);

  // Memoized latest documents calculation
  const latestDocuments = useMemo(() => {
    if (!showLatestDocuments) return null;

    try {
      const matchedIds = new Set(
        categorizedDocuments.flatMap((cat) => cat.content.map((doc) => doc.id))
      );
      return {
        heading: 'Latest Documents',
        content: filteredContent.filter((doc) => !matchedIds.has(doc.id)),
        isOpen: openSections.has('Latest Documents'),
        onClick: () => toggleSection('Latest Documents'),
        layout: {},
      };
    } catch (err) {
      const errorMessage = 'Failed to process latest documents';
      setError(errorMessage);
      onError?.(errorMessage);
      return null;
    }
  }, [
    filteredContent,
    categorizedDocuments,
    openSections,
    toggleSection,
    showLatestDocuments,
    onError,
  ]);

  // Check if there are any documents to display
  const hasDocuments = content.length > 0;
  const hasFilteredDocuments = filteredContent.length > 0;
  const hasAnyContent =
    categorizedDocuments.some((cat) => cat.content.length > 0) ||
    (latestDocuments && latestDocuments.content.length > 0);

  // Error state
  if (error) {
    return (
      <Container className={className}>
        <ErrorContainer>
          <p>Error: {error}</p>
          <RetryButton
            type="button"
            onClick={() => {
              setError(null);
              setIsLoading(false);
            }}
          >
            Retry
          </RetryButton>
        </ErrorContainer>
      </Container>
    );
  }

  // Empty state when no documents at all
  if (!hasDocuments) {
    return (
      <Container className={className}>
        <EmptyStateContainer>
          <EmptyStateTitle>No Documents Available</EmptyStateTitle>
          <EmptyStateMessage>
            There are no documents to display at this time.
          </EmptyStateMessage>
        </EmptyStateContainer>
      </Container>
    );
  }

  // Empty state when search returns no results
  if (searchable && searchTerm.trim() && !hasFilteredDocuments) {
    return (
      <Container className={className}>
        {searchable && (
          <SearchContainer>
            <SearchInput
              type="text"
              placeholder="Search documents by name, purpose, or ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              aria-label="Search documents"
            />
          </SearchContainer>
        )}
        <EmptyStateContainer>
          <EmptyStateTitle>No Search Results</EmptyStateTitle>
          <EmptyStateMessage>
            No documents found matching "{searchTerm}". Try adjusting your
            search terms.
          </EmptyStateMessage>
        </EmptyStateContainer>
      </Container>
    );
  }

  return (
    <Container className={className}>
      {searchable && (
        <SearchContainer>
          <SearchInput
            type="text"
            placeholder="Search documents by name, purpose, or ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            aria-label="Search documents"
          />
        </SearchContainer>
      )}
      <Accordion allowMultipleExpanded={allowMultipleExpanded}>
        <>
          {categorizedDocuments.map((document, key) => (
            <AccordionItem
              key={String(key)}
              isOpen={document.isOpen}
              onClick={document.onClick}
              layout={document.layout}
              aria-label={`${document.heading} section with ${document.content.length} documents`}
            >
              <AccordionHeading>{document.heading}</AccordionHeading>
              <AccordionContent>
                <JobImageListItem
                  jobImageItems={document.content}
                  usecase={'DocumentView'}
                  mediaType={'image'}
                  itemClickApiConfig={itemClickConfig}
                  fileDataKey={fileDataKey}
                  category={document.heading}
                  noDataMessage={`No ${document.heading} to display`}
                />
              </AccordionContent>
            </AccordionItem>
          ))}
        </>
        {latestDocuments && (
          <AccordionItem
            key={'latest-documents'}
            isOpen={latestDocuments.isOpen}
            onClick={latestDocuments.onClick}
            layout={latestDocuments.layout}
            aria-label={`Latest Documents section with ${latestDocuments.content.length} documents`}
          >
            <AccordionHeading>{latestDocuments.heading}</AccordionHeading>
            <AccordionContent>
              <>
                <LatestDocumentsSubtitle>
                  Latest Invoice, Quotation and Report
                </LatestDocumentsSubtitle>
                <JobImageListItem
                  jobImageItems={latestDocuments.content}
                  usecase={'DocumentView'}
                  mediaType={'image'}
                  itemClickApiConfig={itemClickConfig}
                  fileDataKey={fileDataKey}
                  noDataMessage={`No documents to display`}
                />
              </>
            </AccordionContent>
          </AccordionItem>
        )}
      </Accordion>
    </Container>
  );
}
