import styled, { CSSProperties } from 'styled-components';

import Keycloak from 'keycloak-js';
import { ComponentPropsWithoutRef, useMemo } from 'react';
import { Accordion } from '../../Components/Accordion/Accordion/Accordion';
import { AccordionContent } from '../../Components/Accordion/AccordionContent/AccordionContent';
import { AccordionHeading } from '../../Components/Accordion/AccordionHeading/AccordionHeading';
import { AccordionItem } from '../../Components/Accordion/AccordionItem/AccordionItem';
import { IconTypes } from '../../Components/Icons';
import {
  ItemClickApiConfig,
  JobImageListItem,
} from '../../Components/JobImage/JobImageListItem/JobImageListItem';

type DocumentFile = Record<string, any>;

type Category = {
  heading: string;
  content: DocumentFile[];
  isOpen: boolean;
  onClick?: () => void;
  layout?: CSSProperties;
};

const categories = [
  {
    heading: 'Photos',
    match: (doc: DocumentFile) =>
      /image/i.test(doc.purpose || '') || /jpg/i.test(doc.filename || ''),
  },
  {
    heading: 'Quotations',
    match: (doc: DocumentFile) => /quotation/i.test(doc.purpose || ''),
  },
  {
    heading: 'Invoices',
    match: (doc: DocumentFile) => /invoice/i.test(doc.purpose || ''),
  },
  {
    heading: 'Reports',
    match: (doc: DocumentFile) => /report/i.test(doc.purpose || ''),
  },
];

const LatestDocumentsSubtitle = styled.div`
  font-size: 14px;
  font-weight: 400;
  line-height: 16.94px;
  color: #c4c4c4;
  margin: ${(props) => props.theme.SpacingXl};
  text-align: center;
`;

const Container = styled.div`
  display: grid;
  grid-template-rows: auto 1fr;
  width: 100%;
`;

/**
 * Renders a view for displaying a list of documents in an accordion format.
 * @param documents An array of documents to display in the view.
 * @returns JSX element representing the documents view.
 */

export function DocumentsView({
  content,
  onItemClickConfig,
  fileDataKey = 'file',
  _keycloak,
}: {
  content: DocumentFile[];
  onItemClickConfig?: ItemClickApiConfig;
  fileDataKey?: string;
  _keycloak?: Keycloak;
}) {
  const itemClickConfig = useMemo(() => {
    console.log('propsData', { content, onItemClickConfig, fileDataKey });
    return {
      ...onItemClickConfig,
      token: onItemClickConfig?.token
        ? onItemClickConfig.token
        : _keycloak?.token,
    };
  }, [_keycloak, onItemClickConfig]) as ItemClickApiConfig;

  // Categorize documents
  const categorized: Category[] = categories.map((cat) => ({
    heading: cat.heading,
    content: content.filter(cat.match),
    isOpen: false,
    onClick: () => console.log('clicked.....................'),
    layout: {},
  }));

  // Find "Latest Documents" (not matched by any above)
  const matchedIds = new Set(
    categorized.flatMap((cat) => cat.content.map((doc) => doc.id))
  );
  const latestDocuments = {
    heading: 'Latest Documents',
    content: content.filter((doc) => !matchedIds.has(doc.id)),
    isOpen: false,
    onClick: () => console.log('clicked.....................!'),
    layout: {},
  };

  return (
    <Container>
      <Accordion allowMultipleExpanded={true}>
        <>
          {categorized.map((document, key) => (
            <AccordionItem
              key={String(key)}
              isOpen={document.isOpen}
              onClick={document.onClick}
              layout={document.layout}
            >
              <AccordionHeading>{document.heading}</AccordionHeading>
              <AccordionContent>
                <JobImageListItem
                  jobImageItems={document.content}
                  usecase={'DocumentView'}
                  mediaType={'image'}
                  itemClickApiConfig={itemClickConfig}
                  fileDataKey={fileDataKey}
                  category={document.heading}
                  noDataMessage={`No ${document.heading} to display`}
                />
              </AccordionContent>
            </AccordionItem>
          ))}
        </>
        <AccordionItem
          key={'latest-documents'}
          isOpen={latestDocuments?.isOpen}
          onClick={latestDocuments.onClick}
          layout={latestDocuments.layout}
        >
          <AccordionHeading>{latestDocuments.heading}</AccordionHeading>
          <AccordionContent>
            <>
              <LatestDocumentsSubtitle>
                Latest Invoice, Quotation and Report
              </LatestDocumentsSubtitle>
              <JobImageListItem
                jobImageItems={latestDocuments.content}
                usecase={'DocumentView'}
                mediaType={'image'}
                itemClickApiConfig={itemClickConfig}
                fileDataKey={fileDataKey}
                noDataMessage={`No documents to display`}
              />
            </>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </Container>
  );
}
