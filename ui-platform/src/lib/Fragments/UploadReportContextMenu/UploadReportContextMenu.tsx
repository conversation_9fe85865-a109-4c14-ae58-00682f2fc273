import React, { useState } from 'react';
import styled from 'styled-components';
import { useForm, FormProvider } from 'react-hook-form';
import { UploadDocument } from '../UploadDocument';
import { FormBuilder } from '../form-builder';
import { PDFPreview } from '../PDFviewer';
import { UploadImage } from '../UploadImage/UploadImage';

const FileUploadTypes = {
  invoicePDF: 'invoicePDF',
  reportPDF: 'reportPDF',
  photo: 'photo',
} as const;
type FileUploadType = typeof FileUploadTypes[keyof typeof FileUploadTypes];

const ButtonRow = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  justify-content: center;
  width: 100%;
`;

const StyledButton = styled.button<{ selected: boolean }>`
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  border: 1px solid #bbb;
  background: transparent;
  color: #bbb;
  cursor: pointer;
  opacity: ${({ selected }) => (selected ? 1 : 0.85)};
  box-shadow: none;
  outline: none;
`;

const UploadContainer = styled.div`
  padding: 32px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
`;

const UPLOAD_AREA_WIDTH = '90vw';
const UPLOAD_AREA_MAX_WIDTH = '1600px';
const UPLOAD_AREA_HEIGHT = '600px';

const GridLayout = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 0;
  align-items: stretch;
  width: 100%;
  height: 100%;
  min-width: 900px;
  max-width: ${UPLOAD_AREA_MAX_WIDTH};
  min-height: ${UPLOAD_AREA_HEIGHT};
  max-height: 80vh;
`;

const PDFPanel = styled.div`
  /* background: #222; */
  min-width: 0;
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
`;

const FieldsPanel = styled.div`
  /* background: #181818; */
  padding: 32px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: 100%;
`;

const RemoveFileButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 8px;
  margin-bottom: 32px;
`;

const RemoveFileButton = styled.button`
  background: #222;
  border: 1px solid #e53935;
  color: #e53935;
  padding: 8px 20px 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
`;

const UploadArea = styled.div`
  min-width: 900px;
  width: ${UPLOAD_AREA_WIDTH};
  max-width: ${UPLOAD_AREA_MAX_WIDTH};
  min-height: ${UPLOAD_AREA_HEIGHT};
  max-height: 80vh;
  /* background: #111; */
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 40px auto 0 auto;
`;

const defaultFormConfig = {
  style: {},
  controls: [
    {
      type: 'plain-text',
      name: 'invoice_amount',
      label: 'Enter Invoice Amount',
      css: { wrapper: { width: '100%', marginBottom: 24 } },
    },
    {
      type: 'plain-text',
      name: 'invoice_number',
      label: 'Enter Invoice Number',
      css: { wrapper: { width: '100%', marginBottom: 24 } },
    },
    {
      type: 'plain-text',
      name: 'confirm_excess',
      label: 'Confirm Excess',
      css: { wrapper: { width: '100%' } },
    },
  ] as any, 
};

const defaultFormValues = { invoice_amount: '', invoice_number: '', confirm_excess: '' };

const UploadDocuments: React.FC = () => {
  const [uploadType, setUploadType] = useState<FileUploadType | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [showFields, setShowFields] = useState(false);

  // Use any to avoid strict typing issues with UploadDocument
  const methods = useForm<any>({ defaultValues: { uploadedFile: [] } });

  // Reset state on upload type change
  React.useEffect(() => {
    setPdfUrl(null);
    setShowFields(false);
    methods.reset({ uploadedFile: [] });
  }, [uploadType]);

  // PDF/Report PDF upload logic
  const handlePDFUpload = (files: FileList | File[]) => {
    const arr: File[] = Array.from(files as FileList | File[]);
    if (arr.length > 0 && arr[0] instanceof File) {
      const url = URL.createObjectURL(arr[0]);
      setPdfUrl(url);
      setShowFields(true);
    }
  };

  const handleRemove = () => {
    setPdfUrl(null);
    setShowFields(false);
    methods.reset({ uploadedFile: [] });
  };

  return (
    <UploadContainer>
      <ButtonRow>
        <StyledButton
          selected={uploadType === FileUploadTypes.invoicePDF}
          onClick={() => setUploadType(FileUploadTypes.invoicePDF)}
        >
          Upload invoice PDF
        </StyledButton>
        <StyledButton
          selected={uploadType === FileUploadTypes.reportPDF}
          onClick={() => setUploadType(FileUploadTypes.reportPDF)}
        >
          Upload report PDF
        </StyledButton>
        <StyledButton
          selected={uploadType === FileUploadTypes.photo}
          onClick={() => setUploadType(FileUploadTypes.photo)}
        >
          Upload image
        </StyledButton>
      </ButtonRow>
      <UploadArea>
        <FormProvider {...methods}>
          {/* Invoice PDF */}
          {uploadType === FileUploadTypes.invoicePDF && !pdfUrl && (
            <div style={{ width: '100%', height: '100%' }}>
              <UploadDocument
                fileProps={[
                  {
                    dragAndDropMessage: 'Upload Document',
                    onSelect: (files: FileList) => handlePDFUpload(files),
                  },
                ]}
                name="uploadedFile"
                control={methods.control as any}
                onDrop={handlePDFUpload}
              />
            </div>
          )}
          {uploadType === FileUploadTypes.invoicePDF && pdfUrl && (
            <GridLayout>
              <PDFPanel>
                <div style={{ width: '100%', height: '100%' }}>
                  <PDFPreview
                    documents={[
                      {
                        documentUrl: pdfUrl,
                        isBase64: false,
                      },
                    ]}
                  />
                </div>
                <RemoveFileButtonContainer>
                  <RemoveFileButton onClick={handleRemove}>Remove File</RemoveFileButton>
                </RemoveFileButtonContainer>
              </PDFPanel>
              <FieldsPanel>
                <FormBuilder
                  config={defaultFormConfig as any}
                  defaultValues={defaultFormValues}
                  isStory
                />
              </FieldsPanel>
            </GridLayout>
          )}
          {/* Report PDF */}
          {uploadType === FileUploadTypes.reportPDF && !pdfUrl && (
            <div style={{ width: '100%', height: '100%' }}>
              <UploadDocument
                fileProps={[
                  {
                    dragAndDropMessage: 'Upload Document',
                    onSelect: (files: FileList) => handlePDFUpload(files),
                  },
                ]}
                name="uploadedFile"
                control={methods.control as any}
                onDrop={handlePDFUpload}
              />
            </div>
          )}
          {uploadType === FileUploadTypes.reportPDF && pdfUrl && (
            <GridLayout style={{ gridTemplateColumns: '1fr' }}>
              <PDFPanel>
                <div style={{ width: '100%', height: '100%' }}>
                  <PDFPreview
                    documents={[
                      {
                        documentUrl: pdfUrl,
                        isBase64: false,
                      },
                    ]}
                  />
                </div>
                <RemoveFileButtonContainer>
                  <RemoveFileButton onClick={handleRemove}>Remove File</RemoveFileButton>
                </RemoveFileButtonContainer>
              </PDFPanel>
            </GridLayout>
          )}
          {/* Image Upload */}
          {uploadType === FileUploadTypes.photo && (
            <div style={{ width: '100%', height: '100%' }}>
              <UploadImage storeKey="uploadReportImage" dragAndDropMessage="Drag and drop an image, or browse" />
            </div>
          )}
        </FormProvider>
      </UploadArea>
    </UploadContainer>
  );
};

export default UploadDocuments;
