import { DropzoneProps } from 'react-dropzone';
import { Control, FieldError } from 'react-hook-form';
import { ActionConfig } from 'ui-platform/src/lib/Engine';
import { IconTypes } from '../../../Components/Icons/svgs';
import { Files } from '../../../Utilities/fileUploadMimeTypes';
import { BaseControlConfig } from './base.control.config';

export interface DragAndDropControlConfig extends BaseControlConfig {
  uploadState: 'activeFile' | 'error' | 'default' | 'focus' | undefined;
  type: 'drag-and-drop';
  onDrop?: DropzoneProps['onDrop'];
  toggleButton?: boolean;
  iconLeft?: IconTypes;
  iconRight?: IconTypes;
  error?: FieldError | undefined | null;
  emptyText?: string;
  control?: Control;
  className?: string;
  url: string;
  action: string;
  purpose: string;
  pathToRefId?: string;
  list?: 'True' | 'False'; // boolean;
  documentSrc?: 'staff' | 'sp';
  fileTypesAllowed?: Files[];
  fileSizeLimit?: number;
  // New properties for base64 upload support
  uploadMode?: 'binary' | 'base64';
  jobId?: string | number;
  onBase64Upload?:
    | ((data: {
        data: string;
        filename: string;
        job_id: string | number;
        purpose: string;
      }) => void)
    | ActionConfig[];
}

export interface DragAndDropInMemoryControlConfig extends BaseControlConfig {
  uploadState: 'activeFile' | 'error' | 'default' | 'focus' | undefined;
  type: 'drag-and-drop-in-memory';
  onDrop?: DropzoneProps['onDrop'];
  toggleButton?: boolean;
  iconLeft?: IconTypes;
  iconRight?: IconTypes;
  error?: FieldError | undefined | null;
  emptyText?: string;
  control?: Control;
  className?: string;
  url: string;
  action: string;
  purpose: string;
  pathToRefId?: string;
  list?: 'True' | 'False'; // boolean;
  documentSrc?: 'staff' | 'sp';
  fileTypesAllowed?: Files[];
  fileSizeLimit?: number;
  // New properties for base64 upload support
  uploadMode?: 'binary' | 'base64';
  jobId?: string | number;
  onBase64Upload?:
    | ((data: {
        data: string;
        filename: string;
        job_id: string | number;
        purpose: string;
      }) => void)
    | ActionConfig[];
}
