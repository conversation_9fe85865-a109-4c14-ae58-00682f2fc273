import Keycloak from 'keycloak-js';
import { useEffect } from 'react';
import { isAborted } from 'zod';
import { ErrorState, getErrorStore, useAppStore } from '../../Engine';

type GetOpenTasksProps = {
  apiUrl?: string;
  apiEnv?: string;
  callMethod?: 'GET' | 'POST';
  timeoutPeriod?: number;
  _keycloak?: Keycloak;
  isAdmin?: boolean;
  isAuthorised?: boolean;
  errorKey?: string;
};

const call = async ({
  url,
  method,
  bodyData,
  token,
  errorKey = `sp-team-member-list-fetch-call`,
}: {
  url: string;
  method: 'GET' | 'POST';
  bodyData?: { [key: string]: any };
  token?: string;
  errorKey?: string;
}) => {
  const errorStore = getErrorStore() as ErrorState;

  const fetchHeaders = new Headers({});
  fetchHeaders.append('Content-Type', 'application/json');
  fetchHeaders.append('Authorization', `Bearer ${token}`);

  const requestInit: RequestInit = {
    method: method,
    headers: fetchHeaders,
  };

  if (method === 'POST' && bodyData) {
    requestInit.body = JSON.stringify(bodyData);
  }

  try {
    const response = await fetch(url, requestInit);

    if (response.ok) {
      return await response.json();
    } else {
      errorStore.addError({
        key: errorKey,
        message:
          `${response.statusText}: Please try refresh your page.` ||
          'Unknown error: Try refreshing the website',
        source: 'server',
        stackTrace:
          `Status ${response.status}: ${response.statusText}` || undefined,
      });
      throw new Error('Failed to fetch available jobs');
    }
  } catch (error) {
    errorStore.addError({
      key: errorKey,
      message:
        error instanceof Error
          ? `${error.message}: Please contact field-ops for assistance`
          : 'Unknown error: Try refreshing the website',
      source: 'server',
      stackTrace: error instanceof Error ? error.stack : undefined,
    });
    return null;
  }
};

export const useGetOpenTasks = async ({
  apiUrl = '/api/v1/task_actions/get_tasks',
  timeoutPeriod = 1800000,
  apiEnv = 'VITE_SP_SERVER',
  callMethod = 'POST',
  _keycloak,
  isAdmin,
  isAuthorised,
  errorKey,
}: GetOpenTasksProps) => {
  const setState = useAppStore((state: any) => state?.setState);
  const adminTasks = useAppStore((state: any) => state?.admin_tasks);
  useEffect(() => {
    if (!isAdmin || !isAuthorised) return () => undefined;
    const fetchStaffMembers = async () => {
      const openTasks = await call({
        url: `${(import.meta as any).env[apiEnv]}${apiUrl}`,
        method: callMethod,
        bodyData: [],
        token: _keycloak?.token,
      });

      if (openTasks && Array.isArray(openTasks?.payload)) {
        return openTasks.payload as any[];
      }
      return [];
    };

    const updateTasks = async () => {
      let pendingTasks;
      if (adminTasks && adminTasks.length > 0) {
        pendingTasks = adminTasks.length;
      } else {
        const tasks = await fetchStaffMembers();
        pendingTasks = tasks.length;
      }

      setState((state: any) => ({
        ...state,
        pending_admin_tasks: pendingTasks,
      }));
    };

    updateTasks();
    const intervalId = setInterval(updateTasks, timeoutPeriod);
    return () => clearInterval(intervalId);
  }, [
    _keycloak?.token,
    apiEnv,
    apiUrl,
    callMethod,
    // setState,
    timeoutPeriod,
    adminTasks,
    isAdmin,
  ]);
  return;
};
