import type { Meta, StoryObj } from '@storybook/react';
import { ActionPanelRecentActivity } from './ActionPanelRecentActivity';

const meta: Meta<typeof ActionPanelRecentActivity> = {
  component: ActionPanelRecentActivity,
  title: 'Fragments/ActionPanelRecentActivity',
  parameters: {
    layout: 'padded',
  },
};

export default meta;

type Story = StoryObj<typeof ActionPanelRecentActivity>;

export const Overview: Story = {
  args: {
    userID: 12345,
    showClearButton: true,
    onItemClick: (claim_num: string, applicant: string) => {
      console.log('Item clicked:', { claim_num, applicant });
      alert(`Clicked on claim: ${claim_num} for applicant: ${applicant}`);
    },
  },
};

export const WithoutClearButton: Story = {
  args: {
    userID: 12345,
    showClearButton: false,
    onItemClick: (claim_num: string, applicant: string) => {
      console.log('Item clicked:', { claim_num, applicant });
      alert(`Clicked on claim: ${claim_num} for applicant: ${applicant}`);
    },
  },
};

export const DifferentUser: Story = {
  args: {
    userID: 67890,
    showClearButton: true,
    onItemClick: (claim_num: string, applicant: string) => {
      console.log('Item clicked:', { claim_num, applicant });
      alert(`Clicked on claim: ${claim_num} for applicant: ${applicant}`);
    },
  },
}; 