import React from 'react';
import { RecentActivityList } from '../../Components/RecentActivity/RecentActivityList';
import { useRecentActivity } from '../../Hooks/useRecentActivity';

interface ActionPanelRecentActivityProps {
  userID: number;
  onItemClick?: (claim_num: string, applicant: string) => void;
  showClearButton?: boolean;
}

export function ActionPanelRecentActivity({ 
  userID, 
  onItemClick, 
  showClearButton = true 
}: ActionPanelRecentActivityProps) {
    const { recentActivities } = useRecentActivity();
    
    // Debug logging
    console.log('ActionPanelRecentActivity render:', {
      userID,
      allActivities: recentActivities,
      userActivities: recentActivities.filter(item => item.userID === userID)
    });

    return (
        <div>
            <RecentActivityList
                userID={userID}
                onItemClick={onItemClick}
                showClearButton={showClearButton}
            />
        </div>
    )
}